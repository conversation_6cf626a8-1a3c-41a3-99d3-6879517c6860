export default ({ app }, inject) => {
  // Firebase SDK مُثبت ضمن @nuxtjs/firebase، فممكن تستخدمه مباشرة
  const messaging = app.$fire.messaging

  console.log('📥 Firebase Messaging:', messaging)

  if (process.client && messaging) {
    messaging.onMessage((payload) => {
      console.log('📥 رسالة جاية في الـ foreground:', payload)
      const currentUnread = app.store.getters['localStorage/get_unread_notifications'];
      app.store.commit('localStorage/SET_UNREAD_NOTIFICATION', currentUnread + 1);
      
    })

    inject('messaging', messaging)
  }
}
